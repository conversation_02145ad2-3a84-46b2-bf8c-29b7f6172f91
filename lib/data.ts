// lib/data.ts
export type Modality = "Kraniosakrální terapie" | "Somatic Experiencing" | "Fyzioterapie";

export const MODALITY: Modality[] = [
  "Kraniosakrální terapie",
  "Somatic Experiencing",
  "Fyzioterapie",
];

export const KRAJE = [
  "Hlavní město Praha",
  "Středočeský kraj",
  "Jihočeský kraj",
  "Plzeňský kraj",
  "Karlovarský kraj",
  "Ústecký kraj",
  "Liberecký kraj",
  "Královéhradecký kraj",
  "Pardubický kraj",
  "Vysočina",
  "Jihomoravský kraj",
  "Olomoucký kraj",
  "Moravskoslezský kraj",
  "Zlínský kraj",
];

export const THERAPISTS = [
  {
    id: "t1",
    slug: "hana-chocholata",
    name: "<PERSON>a <PERSON>latá, DiS.",
    modalities: ["Kraniosakrální terapie"],
    city: "Praha",
    region: "Hlavní město Praha",
    languages: ["CZ"],
    rating: 4.8,
    reviewsCount: 39,
    education: ["Dlouhodobý výcvik KST", "Anatomie & fyziologie"],
    associations: ["Česká kraniosakrální asociace"],
    specializations: ["Trauma‑informovaný přístup", "Dětská péče", "Migrény"],
    phone: "+420 777 000 000",
    email: "<EMAIL>",
    website: "https://example.cz",
    bookingUrl: "https://example.cz/rezervace",
    address: "Na Příkopě 1, Praha 1",
    location: { lat: 50.087, lng: 14.421 },
    bio: "Vědomá, jemná práce s nervovým systémem. Bezpečný prostor a respekt k hranicím.",
  },
  {
    id: "t2",
    slug: "jan-novak-sep",
    name: "Jan Novák, SEP",
    modalities: ["Somatic Experiencing"],
    city: "Brno",
    region: "Jihomoravský kraj",
    languages: ["CZ", "EN"],
    rating: 4.6,
    reviewsCount: 24,
    education: ["Somatic Experiencing® – 6–8 modulů během 2½–3 let"],
    associations: ["Somatic Experiencing® International"],
    specializations: ["PTSD", "Úzkost", "Somatizace"],
    phone: "+420 776 111 222",
    email: "<EMAIL>",
    website: "https://example.cz",
    bookingUrl: "https://example.cz/rezervace",
    address: "Náměstí Svobody 12, Brno",
    location: { lat: 49.195, lng: 16.607 },
    bio: "Stabilizace, titrace a práce s tělesnými projevy traumatu v bezpečném tempu.",
  },
  {
    id: "t3",
    slug: "petra-dvorakova",
    name: "Mgr. Petra Dvořáková",
    modalities: ["Fyzioterapie", "Kraniosakrální terapie"],
    city: "Ostrava",
    region: "Moravskoslezský kraj",
    languages: ["CZ"],
    rating: 4.9,
    reviewsCount: 58,
    education: ["Bakalářské studium fyzioterapie (regulované povolání)", "KST postgraduate"],
    associations: ["Unie fyzioterapeutů", "ČKA"],
    specializations: ["Chronická bolest", "Pánevní dno", "Sportovní zátěž"],
    phone: "+420 605 222 333",
    email: "<EMAIL>",
    website: "https://example.cz",
    bookingUrl: "https://example.cz/rezervace",
    address: "Stodolní 5, Ostrava",
    location: { lat: 49.835, lng: 18.292 },
    bio: "Spojuji fyzio, KST a jemné dotekové techniky se zaměřením na integritu pohybu.",
  },
] as const;

export const THERAPISTS_MAP = Object.fromEntries(THERAPISTS.map(t => [t.slug, t]));