// app/page.tsx
"use client";
import { useMemo, useState } from "react";
import Filters from "@/components/Filters";
import MapClient from "@/components/MapClient";
import TherapistCard from "@/components/TherapistCard";
import Section from "@/components/Section";
import { THERAPISTS, KRAJE, MODALITY } from "@/lib/data";

export default function HomePage() {
  const [q, setQ] = useState("");
  const [modality, setModality] = useState<string>("");
  const [region, setRegion] = useState<string>("");
  const [minRating, setMinRating] = useState<number>(0);
  const [sort, setSort] = useState<string>("smart");
  const [selectedId, setSelectedId] = useState<string | null>(null);

  const filtered = useMemo(() => {
    const list = THERAPISTS.filter(t =>
      (q ? (t.name + " " + t.city + " " + t.specializations.join(" ") + " " + t.modalities.join(" ")).toLowerCase().includes(q.toLowerCase()) : true) &&
      (modality ? t.modalities.includes(modality as any) : true) &&
      (region ? t.region === region : true) &&
      t.rating >= minRating
    ).sort((a,b) => {
      if (sort === "smart") {
        const score = (x: typeof THERAPISTS[number]) => x.rating * 0.8 + Math.log(1 + x.reviewsCount) * 0.4;
        return score(b) - score(a);
      }
      if (sort === "rating") return b.rating - a.rating;
      return a.name.localeCompare(b.name);
    });
    return list;
  }, [q, modality, region, minRating, sort]);

  const selected = useMemo(() => THERAPISTS.find(t => t.id === selectedId) || null, [selectedId]);

  return (
    <div className="px-4 py-6 mx-auto max-w-7xl">
      <section className="mb-6 text-center md:text-left">
        <h1 className="text-3xl font-semibold">Najděte bezpečnou péči blízko vás</h1>
        <p className="mt-1 text-slate-600">Bez cookies, bez reklam. Ověření terapeuti.</p>
      </section>

      <Filters
        q={q}
        setQ={setQ}
        modality={modality}
        setModality={setModality}
        region={region}
        setRegion={setRegion}
        minRating={minRating}
        setMinRating={setMinRating}
        sort={sort}
        setSort={setSort}
        kraje={KRAJE}
        modalityList={MODALITY}
      />

      <div className="grid gap-6 md:grid-cols-12">
        <div className="md:col-span-5 rounded-2xl border border-slate-200 overflow-hidden">
          <MapClient therapists={filtered} selectedId={selectedId} onSelect={setSelectedId} selected={selected} />
        </div>
        <div className="md:col-span-7">
          <ul className="space-y-3">
            {filtered.map(t => (
              <li key={t.id}>
                <TherapistCard data={t} onOpen={() => setSelectedId(t.id)} />
              </li>
            ))}
            {filtered.length === 0 && (
              <li className="rounded-2xl border border-slate-200 p-10 text-center text-sm text-slate-600">Žádné výsledky. Upravte filtry nebo zkuste jiné klíčové slovo.</li>
            )}
          </ul>
        </div>
      </div>

      <Section title="Jak vybrat praktikanta" id="jak-vybrat-teaser">
        <p className="text-sm text-slate-700">Hledejte dlouhodobý výcvik, bezpečí a respekt, etiku a transparentnost. <a className="underline" href="/jak-vybrat">Více zde</a>.</p>
      </Section>
    </div>
  );
}