// app/terapeuti/[slug]/page.tsx
import { THERAPISTS_MAP } from "@/lib/data";
import StarRating from "@/components/StarRating";
import Badge from "@/components/Badges";

interface Props { params: { slug: string } }

export default function TherapistDetailPage({ params }: Props) {
  const t = THERAPISTS_MAP[params.slug];
  if (!t) return <div className="px-4 py-6">Profil nenale<PERSON>.</div>;

  return (
    <div className="px-4 py-6 mx-auto max-w-5xl">
      <section className="flex flex-col md:flex-row gap-6 items-start">
        <div className="h-28 w-28 rounded-2xl bg-slate-100" />
        <div className="flex-1">
          <h1 className="text-3xl font-semibold">{t.name}</h1>
          <div className="mt-1 text-slate-700">{t.city} • {t.region}</div>
          <div className="mt-2"><StarRating value={t.rating} /> <span className="text-sm text-slate-600 ml-2">({t.reviewsCount} recenzí)</span></div>
          <div className="mt-2 flex flex-wrap gap-1.5">{t.modalities.map((m: string) => <Badge key={m}>{m}</Badge>)}{t.specializations.map((s: string, i: number) => <Badge key={i}>{s}</Badge>)}</div>
          <div className="mt-3 flex gap-2">
            {t.bookingUrl && <a className="rounded-xl bg-teal-700 px-4 py-2 text-sm text-white" href={t.bookingUrl} target="_blank">Objednat se</a>}
            {t.website && <a className="rounded-xl border border-slate-300 px-4 py-2 text-sm" href={t.website} target="_blank">Web</a>}
            {t.email && <a className="rounded-xl border border-slate-300 px-4 py-2 text-sm" href={`mailto:${t.email}`}>Napsat</a>}
          </div>
        </div>
      </section>

      <section className="mt-8 grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2 space-y-6">
          <div>
            <h2 className="text-lg font-semibold">O mně</h2>
            <p className="mt-2 text-slate-700 whitespace-pre-line">{t.bio}</p>
          </div>
          <div>
            <h2 className="text-lg font-semibold">Vzdělání a kvalifikace</h2>
            <ul className="mt-2 list-disc list-inside text-slate-700">
              {t.education.map((e: string, i: number) => <li key={i}>{e}</li>)}
            </ul>
          </div>
          <div>
            <h2 className="text-lg font-semibold">Recenze (ověřené)</h2>
            <p className="mt-2 text-sm text-slate-600">Hodnocení přidáte pomocí jednorázového kódu po sezení. <a className="underline" href="/overeni">Jak ověřujeme</a>.</p>
          </div>
        </div>
        <aside className="space-y-6">
          <div className="rounded-2xl border border-slate-200 p-4">
            <h3 className="font-medium">Praktické informace</h3>
            <ul className="mt-2 text-sm text-slate-700 space-y-1">
              {t.address && <li>{t.address}</li>}
              {t.phone && <li>Tel: {t.phone}</li>}
              <li>Sezení: 60–90 min</li>
              <li>Storno: 24–48 h</li>
            </ul>
          </div>
          <div className="rounded-2xl border border-slate-200 p-4">
            <h3 className="font-medium">Lokace</h3>
            <div className="mt-2 h-48 rounded-xl bg-slate-100 grid place-items-center text-sm text-slate-600">Mapa (embed později)</div>
          </div>
        </aside>
      </section>
    </div>
  );
}