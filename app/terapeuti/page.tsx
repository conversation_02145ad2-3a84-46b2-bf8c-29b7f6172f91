// app/terapeuti/page.tsx
"use client";
import { useMemo, useState } from "react";
import Filters from "@/components/Filters";
import TherapistCard from "@/components/TherapistCard";
import { THERAPISTS, KRAJE, MODALITY } from "@/lib/data";

export default function CatalogPage() {
  const [q, setQ] = useState("");
  const [modality, setModality] = useState<string>("");
  const [region, setRegion] = useState<string>("");
  const [minRating, setMinRating] = useState<number>(0);
  const [sort, setSort] = useState<string>("smart");

  const filtered = useMemo(() => {
    return THERAPISTS.filter(t =>
      (q ? (t.name + " " + t.city + " " + t.specializations.join(" ") + " " + t.modalities.join(" ")).toLowerCase().includes(q.toLowerCase()) : true) &&
      (modality ? t.modalities.includes(modality as any) : true) &&
      (region ? t.region === region : true) &&
      t.rating >= minRating
    ).sort((a,b) => {
      if (sort === "smart") {
        const score = (x: typeof THERAPISTS[number]) => x.rating * 0.8 + Math.log(1 + x.reviewsCount) * 0.4;
        return score(b) - score(a);
      }
      if (sort === "rating") return b.rating - a.rating;
      return a.name.localeCompare(b.name);
    });
  }, [q, modality, region, minRating, sort]);

  return (
    <div className="px-4 py-6 mx-auto max-w-7xl">
      <h1 className="text-2xl font-semibold mb-4">Terapeuti v ČR</h1>
      <Filters
        q={q}
        setQ={setQ}
        modality={modality}
        setModality={setModality}
        region={region}
        setRegion={setRegion}
        minRating={minRating}
        setMinRating={setMinRating}
        sort={sort}
        setSort={setSort}
        kraje={KRAJE}
        modalityList={MODALITY}
      />
      <ul className="mt-4 space-y-3">
        {filtered.map(t => (
          <li key={t.id}>
            <TherapistCard data={t} href={`/terapeuti/${t.slug}`} />
          </li>
        ))}
      </ul>
    </div>
  );
}