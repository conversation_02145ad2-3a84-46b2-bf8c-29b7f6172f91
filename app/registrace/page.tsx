// app/registrace/page.tsx
import Section from "@/components/Section";

export default function RegistrationPage() {
  return (
    <div className="px-4 py-6 mx-auto max-w-4xl">
      <h1 className="text-2xl font-semibold">Přidejte svůj ověřený profil</h1>
      <p className="mt-1 text-slate-700">Bez reklam, bez cookies. Místo, kde lidé hledají bezpečnou péči.</p>

      <Section title="Proč se přidat?">
        <ul className="grid gap-4 md:grid-cols-3 text-sm text-slate-700">
          <li className="rounded-xl border border-slate-200 p-4">Důvěryhodnost: ověřený profil budí důvěru u klientů.</li>
          <li className="rounded-xl border border-slate-200 p-4">Noví klienti bez reklamy: lidé vás najdou podle potřeb a lokality.</li>
          <li className="rounded-xl border border-slate-200 p-4">Komunita & vzdělávání: přístup k tipům a webinářům.</li>
        </ul>
      </Section>

      <Section title="Jak to funguje">
        <ol className="list-decimal list-inside text-slate-700 space-y-1">
          <li>Odešlete žádost</li>
          <li>Potvrďte e-mail (double‑opt‑in)</li>
          <li>Vytvořte profil</li>
          <li>Nahrajte doklady</li>
          <li>Ruční schválení adminem</li>
        </ol>
      </Section>

      <Section title="Pilotní fáze (MVP)">
        <p className="text-slate-700">V pilotu je registrace zdarma. Později přejdeme na členství s rozšířeným profilem a vzděláváním.</p>
      </Section>

      <Section title="Začněte">
        <form className="grid gap-3 max-w-xl">
          <input className="rounded-xl border border-slate-300 px-3 py-2 text-sm" placeholder="Jméno a příjmení" />
          <input className="rounded-xl border border-slate-300 px-3 py-2 text-sm" placeholder="E‑mail (ověření)" />
          <input className="rounded-xl border border-slate-300 px-3 py-2 text-sm" placeholder="Město" />
          <select className="rounded-xl border border-slate-300 px-3 py-2 text-sm">
            <option>Modality</option>
            <option>Kraniosakrální terapie</option>
            <option>Somatic Experiencing</option>
            <option>Fyzioterapie</option>
          </select>
          {/* Honeypot */}
          <input aria-hidden className="hidden" tabIndex={-1} autoComplete="off" placeholder="Nechte prázdné" />
          <div className="text-xs text-slate-600">Anti‑spam: honeypot + časový limit odeslání + ruční ověření.</div>
          <div className="flex gap-2">
            <button className="rounded-xl bg-teal-700 px-4 py-2 text-sm text-white" type="button">Odeslat žádost</button>
            <a href="/overeni" className="rounded-xl border border-slate-300 px-4 py-2 text-sm">Jak ověřujeme</a>
          </div>
        </form>
      </Section>
    </div>
  );
}