// components/MapClient.tsx
"use client";
import { Google<PERSON><PERSON>, Marker, useLoadScript } from "@react-google-maps/api";

export default function MapClient({ therapists, selectedId, onSelect, selected }: { therapists: any[]; selectedId: string | null; onSelect: (id: string)=>void; selected: any | null; }) {
  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_KEY || "YOUR_API_KEY",
    id: "google-map-script",
  });

  const center = selected?.location || { lat: 49.8175, lng: 15.473 };

  if (!isLoaded) return <div className="grid h-[420px] place-items-center text-sm text-slate-600">Načítám mapu…</div>;
  return (
    <GoogleMap
      mapContainerStyle={{ width: "100%", height: 420 }}
      center={center}
      zoom={6}
      options={{
        mapId: process.env.NEXT_PUBLIC_GOOGLE_MAP_ID || "YOUR_MAP_ID",
        disableDefaultUI: true,
        zoomControl: true,
        clickableIcons: false,
        gestureHandling: "greedy",
      }}
    >
      {therapists.map(t => (
        <Marker key={t.id} position={t.location} onClick={()=>onSelect(t.id)} />
      ))}
    </GoogleMap>
  );
}