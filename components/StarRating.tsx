// components/StarRating.tsx
export default function StarRating({ value }: { value: number }) {
  const full = Math.floor(value);
  const half = value - full >= 0.5;
  return (
    <div className="inline-flex items-center" aria-label={`Hodnocení ${value.toFixed(1)} z 5`}>
      {Array.from({ length: 5 }).map((_, i) => (
        <svg key={i} viewBox="0 0 24 24" className={`${i < full ? "fill-current" : half && i === full ? "fill-current opacity-60" : "fill-transparent stroke-current"} h-4 w-4`}>
          <path d="M12 .587l3.668 7.431 8.2 1.193-5.934 5.787 1.401 8.168L12 18.896l-7.335 3.87 1.401-8.168L.132 9.211l8.2-1.193z"/>
        </svg>
      ))}
      <span className="ml-2 text-xs text-slate-600">{value.toFixed(1)}</span>
    </div>
  );
}