// components/Filters.tsx
"use client";

type Props = {
  q: string; setQ: (v: string)=>void;
  modality: string; setModality: (v: string)=>void;
  region: string; setRegion: (v: string)=>void;
  minRating: number; setMinRating: (n: number)=>void;
  sort: string; setSort: (v: string)=>void;
  kraje: string[]; modalityList: string[];
}

export default function Filters({ q,setQ, modality,setModality, region,setRegion, minRating,setMinRating, sort,setSort, kraje, modalityList }: Props) {
  return (
    <div className="mb-4 grid grid-cols-2 gap-2 md:grid-cols-4">
      <input value={q} onChange={e=>setQ(e.target.value)} placeholder="Hledat jméno, město, zaměření…" className="col-span-2 md:col-span-4 rounded-xl border border-slate-300 px-3 py-2 text-sm" />
      <div className="col-span-2 flex flex-wrap gap-2 md:col-span-4">
        {modalityList.map(m => (
          <button key={m} onClick={() => setModality(modality === m ? "" : m)} className={`rounded-full px-3 py-1 text-sm border ${modality === m ? "bg-teal-700 text-white border-teal-700" : "border-slate-300"}`}>{m}</button>
        ))}
      </div>
      <select value={region} onChange={e=>setRegion(e.target.value)} className="rounded-xl border border-slate-300 px-3 py-2 text-sm">
        <option value="">Region</option>
        {kraje.map(k=> <option key={k} value={k}>{k}</option>)}
      </select>
      <select value={minRating} onChange={e=>setMinRating(parseFloat(e.target.value))} className="rounded-xl border border-slate-300 px-3 py-2 text-sm">
        <option value={0}>Hodnocení</option>
        {[0,3.5,4,4.5].map(n => <option key={n} value={n}>{n}+</option>)}
      </select>
      <select value={sort} onChange={e=>setSort(e.target.value)} className="rounded-xl border border-slate-300 px-3 py-2 text-sm">
        <option value="smart">Řadit: doporučené</option>
        <option value="rating">Řadit: hodnocení</option>
        <option value="name">Řadit: jméno</option>
      </select>
      <div className="text-xs text-slate-600 md:col-span-4">Tip: Zkuste „dětská péče“, „úzkost“ nebo „migrény“.</div>
    </div>
  );
}