// components/TherapistCard.tsx
"use client";
import Link from "next/link";
import StarRating from "@/components/StarRating";
import Badge from "@/components/Badges";

export default function TherapistCard({ data, onOpen, href }: { data: any; onOpen?: ()=>void; href?: string }) {
  const content = (
    <div className="rounded-2xl border border-slate-200 p-4 transition hover:shadow-md focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-teal-600">
      <div className="flex items-start gap-4">
        <div className="mt-1 h-12 w-12 shrink-0 rounded-xl bg-slate-100" />
        <div className="flex-1">
          <div className="flex flex-wrap items-center justify-between gap-2">
            <div className="text-base font-semibold">{data.name}</div>
            <StarRating value={data.rating} />
          </div>
          <div className="mt-1 text-sm text-slate-700">{data.city} • {data.region} • {data.languages?.join(", ")}</div>
          <div className="mt-2 flex flex-wrap gap-1.5">
            {data.modalities?.map((m: string) => <Badge key={m}>{m}</Badge>)}
            {data.specializations?.slice(0,3).map((s: string, i: number) => <Badge key={i}>{s}</Badge>)}
          </div>
          <p className="mt-2 line-clamp-2 text-sm text-slate-700">{data.bio}</p>
          <div className="mt-3 flex flex-wrap gap-2">
            {data.bookingUrl && <a className="rounded-xl bg-teal-700 px-3 py-1.5 text-sm text-white" href={data.bookingUrl} target="_blank">Objednat se</a>}
            {data.website && <a className="rounded-xl border border-slate-300 px-3 py-1.5 text-sm" href={data.website} target="_blank">Web</a>}
          </div>
        </div>
      </div>
    </div>
  );

  if (href) return <Link href={href}>{content}</Link>;
  return <button onClick={onOpen} className="w-full text-left">{content}</button>;
}