// components/Header.tsx
import Link from "next/link";

export default function Header() {
  return (
    <header className="sticky top-0 z-40 w-full border-b border-slate-200 bg-white/80 backdrop-blur">
      <div className="mx-auto flex max-w-7xl items-center justify-between px-4 py-3">
        <div className="flex items-center gap-3">
          <img src="/logo.svg" alt="logo" className="h-8 w-8" />
          <div>
            <Link href="/" className="text-lg font-semibold">Mapa ověřených terapeutů</Link>
            <p className="text-xs text-slate-600">Bez cookies. Bez reklam.</p>
          </div>
        </div>
        <nav className="hidden items-center gap-4 md:flex text-sm">
          <Link href="/terapeuti">Najít terapeuta</Link>
          <Link href="/o-projektu">O projektu</Link>
          <Link href="/registrace" className="rounded-xl bg-teal-700 px-3 py-1.5 text-white">Přidat profil</Link>
        </nav>
      </div>
    </header>
  );
}